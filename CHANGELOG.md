# Changelog

## Version 2.0 - Due Date & Notifications Update

### New Features

#### 1. Due Date Management

- Added optional due dates for tasks with both date and time selection
- Tasks can now have deadlines set when creating or editing
- Due dates can be removed at any time

#### 2. Due Soon Section

- New prominent section at the top of the main view showing urgent tasks
- Displays tasks that are:
  - **Overdue**: Past their due date (red indicator)
  - **Due Soon**: Within 24 hours of due date (orange indicator)
- Horizontal scrolling cards for easy browsing
- Section automatically hides when no urgent tasks exist
- Shows formatted due dates like "Today at 3:00 PM" or "Tomorrow at 9:00 AM"

#### 3. Local Notifications

- Automatic notifications scheduled 1 hour before task due time
- Smart notification management:
  - Created when setting a due date
  - Updated when changing a due date
  - Cancelled when completing a task
  - Removed when deleting a task
- Tap notification to open the specific task
- Requires user permission (requested on first launch)

#### 4. Visual Enhancements

- Due date indicators in task cards with clock icon
- Color-coded urgency:
  - Red for overdue tasks
  - Orange for tasks due soon
  - Gray for future tasks
- Warning color added to theme for urgent items
- Gradient background for Due Soon section

### Code Structure Improvements

#### 1. Better Organization

- **Models**: Separated into individual files
  - `Task.swift`: Enhanced with due date properties
  - `TagItem.swift`: Tag model
  - `DependencyID.swift`: Dependency model
- **Services**: New folder for business logic
  - `NotificationManager.swift`: Centralized notification handling
- **ViewModels**: Separated business logic from views
  - `TaskViewModel.swift`: Task operations and data management
- **Extensions**: Reusable functionality
  - `TaskExtensions.swift`: Array helpers for task filtering/sorting
- **Views/TaskComponents**: Modular view components
  - `DueSoonSection.swift`: New urgent tasks section

#### 2. Enhanced Task Model

```swift
// New properties
var dueDate: Date?
var hasNotified: Bool = false

// New computed properties
var isOverdue: Bool
var isDueSoon: Bool
var daysUntilDue: Int?
var formattedDueDate: String?
```

#### 3. Improved Architecture

- Cleaner separation of concerns
- More modular and maintainable code
- Better type safety with proper model separation
- Centralized notification management

### Technical Details

#### Notification System

- Uses `UNUserNotificationCenter` for local notifications
- Notifications scheduled using calendar triggers
- Unique identifiers based on task IDs for easy management
- Foreground notification support enabled

#### UI Updates

- `AddTaskView`: Added due date toggle and date picker
- `TaskEditView`: Added due date editing capabilities
- `TaskCardView`: Shows due date with appropriate urgency indicators
- `ContentView`: Integrated Due Soon section and notification handling

### Bug Fixes

- Fixed import issues with SwiftUI Task vs custom Task model
- Resolved theme color references
- Fixed notification scheduling for proper timing

### Migration Notes

- Existing tasks will not have due dates (optional property)
- No data migration required
- Notification permissions will be requested on next launch

### Future Enhancements (Planned)

- Customizable notification timing (not just 1 hour before)
- Recurring tasks with repeating due dates
- Calendar app integration
- Widget support for due tasks
- Multiple notification reminders
- Snooze functionality for notifications
