//
//  Theme.swift
//  readies
//
//  Created by <PERSON><PERSON> on 2025/6/4.
//
import SwiftUI

struct AppTheme {
    // Common Accent Colors (can be expanded for more tag variety)
    static let accentBlue = Color(hex: "007AFF")
    static let accentPurple = Color(hex: "A020F0")
    static let vibrantGreen = Color(hex: "34C759")
    static let vibrantOrange = Color(hex: "FF9500")
    static let vibrantRed = Color(hex: "FF3B30")
    static let tagColor = Color(hex: "5E6AD2") // A nice purple/blue for tags, like Monday uses

    // Light Mode Specific
    struct Light {
        static let primaryBackground = Color(red: 242/255, green: 242/255, blue: 247/255)
        static let secondaryBackground = Color.white // For cards, forms
        static let tertiaryBackground = Color(red: 229/255, green: 229/255, blue: 234/255) // For less prominent elements

        static let primaryText = Color.primary
        static let secondaryText = Color.secondary

        static let tagBackground = tagColor.opacity(0.15)
        static let tagText = tagColor

        static let completed = vibrantGreen
        static let ready = accentPurple
        static let blocked = vibrantOrange
        static let delete = vibrantRed
        static let inactiveFAB = Color(red: 199/255, green: 199/255, blue: 204/255)
    }

    // Dark Mode Specific
    struct Dark {
        static let primaryBackground = Color(red: 28 / 255, green: 28 / 255, blue: 30 / 255) // Very dark gray
        static let secondaryBackground = Color(red: 44 / 255, green: 44 / 255, blue: 46 / 255) // Card background
        static let tertiaryBackground = Color(red: 60 / 255, green: 60 / 255, blue: 62 / 255) // Buttons, fields

        static let primaryText = Color(red: 240 / 255, green: 240 / 255, blue: 245 / 255) // Off-white
        static let secondaryText = Color(red: 150 / 255, green: 150 / 255, blue: 155 / 255) // Lighter gray

        static let tagBackground = tagColor.opacity(0.3)
        static let tagText = Color(hex: "A9B0F5") // Lighter version of tagColor for dark mode text

        static let completed = vibrantGreen
        static let ready = accentPurple
        static let blocked = vibrantOrange
        static let delete = vibrantRed
        static let inactiveFAB = Color(red: 80 / 255, green: 80 / 255, blue: 82 / 255)
    }

    // Helper getters
    static func primaryBackgroundColor(for scheme: ColorScheme) -> Color { scheme == .dark ? Dark.primaryBackground : Light.primaryBackground }
    static func secondaryBackgroundColor(for scheme: ColorScheme) -> Color { scheme == .dark ? Dark.secondaryBackground : Light.secondaryBackground }
    static func tertiaryBackgroundColor(for scheme: ColorScheme) -> Color { scheme == .dark ? Dark.tertiaryBackground : Light.tertiaryBackground }

    static func primaryTextColor(for scheme: ColorScheme) -> Color { scheme == .dark ? Dark.primaryText : Light.primaryText }
    static func secondaryTextColor(for scheme: ColorScheme) -> Color { scheme == .dark ? Dark.secondaryText : Light.secondaryText }

    static func tagBackgroundColor(for scheme: ColorScheme) -> Color { scheme == .dark ? Dark.tagBackground : Light.tagBackground }
    static func tagTextColor(for scheme: ColorScheme) -> Color { scheme == .dark ? Dark.tagText : Light.tagText }

    static func completedColor(for scheme: ColorScheme) -> Color { AppTheme.vibrantGreen } // Keep them consistent
    static func readyColor(for scheme: ColorScheme) -> Color { AppTheme.accentPurple }
    static func blockedColor(for scheme: ColorScheme) -> Color { AppTheme.vibrantOrange }
    static func deleteColor(for scheme: ColorScheme) -> Color { AppTheme.vibrantRed }
    static func accentColor(for scheme: ColorScheme) -> Color { AppTheme.accentPurple }
    static func inactiveFABColor(for scheme: ColorScheme) -> Color { scheme == .dark ? Dark.inactiveFAB : Light.inactiveFAB }
    static func warningColor(for scheme: ColorScheme) -> Color { AppTheme.vibrantOrange }
}

// Hex color initializer
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default: (a, r, g, b) = (255, 0, 0, 0)
        }
        self.init(.sRGB, red: Double(r) / 255, green: Double(g) / 255, blue: Double(b) / 255, opacity: Double(a) / 255)
    }
}
