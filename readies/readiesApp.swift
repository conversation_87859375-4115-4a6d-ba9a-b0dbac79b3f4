//
//  readiesApp.swift
//  readies
//
//  Created by <PERSON><PERSON> on 2025/4/21.
//

import SwiftData
import SwiftUI

@main
struct readiesApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    
    var body: some Scene {
        WindowGroup {
            ContentView()
        }
        .modelContainer(for: Task.self) { result in
            do {
                let container = try result.get()
                // Enable CloudKit sync
                container.mainContext.autosaveEnabled = true
            } catch {
                print("Failed to configure model container: \(error)")
            }
        }
    }
}

// App Delegate for handling notifications
class AppDelegate: NSObject, UIApplicationDelegate {
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        // Request notification permissions on app launch
        NotificationManager.shared.requestAuthorization()
        return true
    }
}

// @main
// struct TodoApp: App {
//    var body: some Scene {
//        WindowGroup {
//            ContentView()
//        }
//        .modelContainer(for: Task.self) { result in
//            do {
//                let container = try result.get()
//                container.mainContext.automaticallyMergesChangesFromParent = true
//            } catch {
//                print("Failed to configure model container: \(error)")
//            }
//        }
//    }
// }
