import SwiftUI

struct MultiSelectionListView<Item, Label: View>: View where Item: Identifiable, Item: Hashable {
    let allItems: [Item]
    @Binding var selectedItemIDs: Set<Item.ID>
    let labelContent: (Item) -> Label // Closure to define how each item's label is rendered
    let navigationTitle: String

    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme // For potential theming inside the list

    var body: some View {
        NavigationView {
            List {
                ForEach(allItems) { item in
                    HStack {
                        labelContent(item) // Use the provided closure for the label
                        Spacer()
                        if selectedItemIDs.contains(item.id) {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(AppTheme.accentColor(for: colorScheme)) // Assuming AppTheme is accessible
                        } else {
                            Image(systemName: "circle")
                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme)) // Assuming AppTheme
                        }
                    }
                    .contentShape(Rectangle()) // Make the whole row tappable
                    .onTapGesture {
                        toggleSelection(for: item.id)
                    }
                }
            }
            .navigationTitle(Text(navigationTitle))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        // The binding selectedItemIDs is already updated
                        dismiss()
                    }
                }
            }
        }
    }

    private func toggleSelection(for itemID: Item.ID) {
        if selectedItemIDs.contains(itemID) {
            selectedItemIDs.remove(itemID)
        } else {
            selectedItemIDs.insert(itemID)
        }
    }
}

// MARK: - Preview

// Define a sample identifiable item for preview
private struct PreviewListItem: Identifiable, Hashable {
    let id: UUID
    var name: String
}

#Preview {
    let sampleItems = [
        PreviewListItem(id: UUID(), name: "Option 1"),
        PreviewListItem(id: UUID(), name: "Option 2"),
        PreviewListItem(id: UUID(), name: "Option 3"),
        PreviewListItem(id: UUID(), name: "Another Option (4)"),
    ]
    
    @State var previewSelectedIDs = Set<UUID>([sampleItems[1].id])

    return MultiSelectionListView(
        allItems: sampleItems,
        selectedItemIDs: $previewSelectedIDs,
        labelContent: { item in
            Text(item.name) // Simple text label for preview
        },
        navigationTitle: "Select Options"
    )
    // .environmentObject(AppTheme()) // If AppTheme is an EnvironmentObject
}