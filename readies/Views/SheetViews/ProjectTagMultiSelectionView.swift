import SwiftUI

// Helper struct to make tag names Identifiable for MultiSelectionListView
private struct IdentifiableTag: Identifiable, Hashable {
    let id: String // The tag name itself serves as the ID
}

struct ProjectTagMultiSelectionView: View { // Renamed struct
    // All unique tag names available in the system
    let allAvailableTagNames: [String]
    
    // Binding to the set of selected tag names for the current task
    @Binding var selectedTagNames: Set<String>

    // Convert [String] to [IdentifiableTag] for the generic view
    private var identifiableTags: [IdentifiableTag] {
        allAvailableTagNames.map { IdentifiableTag(id: $0) }.sorted { $0.id < $1.id }
    }

    var body: some View {
        MultiSelectionListView(
            allItems: identifiableTags,
            selectedItemIDs: $selectedTagNames, // Set<String> is compatible with Set<IdentifiableTag.ID>
            labelContent: { identifiableTag in
                Text(identifiableTag.id) // Display the tag name
            },
            navigationTitle: "Select Tags"
        )
    }
}

#Preview {
    // Sample data for preview
    let sampleTags = ["urgent", "home", "work", "projectA", "quick"]
    @State var previewSelectedTags = Set<String>(["home", "work"])

    ProjectTagMultiSelectionView( // Updated preview call
        allAvailableTagNames: sampleTags,
        selectedTagNames: $previewSelectedTags
    )
}