import SwiftUI
import SwiftData

struct TaskEditView: View {
    @Bindable var task: Task // Use the qualified name
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    // Temporary state for editing fields if needed, or bind directly
    @State private var editedText: String
    @State private var hasDueDate: Bool
    @State private var dueDate: Date

    // To manage prerequisites selection if you add that feature
    @State private var showingDependencySelector = false
    @State private var temporarySelectedDependencyIDs = Set<UUID>()

    // To manage tags if you add that feature
    @State private var newTagInput: String = ""
    @State private var showingTagSelector = false
    @State private var temporarySelectedTagNames = Set<String>()

    // Query for all tasks to select dependencies from
    @Query(sort: \Task.createdAt, order: .reverse) private var allTasks: [Task]

    // Computed property for all unique tag names from allTasks
    private var allAvailableTagNames: [String] {
        let allTagItems = allTasks.flatMap { $0.tags }
        return Array(Set(allTagItems.map { $0.name })).sorted()
    }

    init(task: Task) {
        self.task = task
        _editedText = State(initialValue: task.text)
        _hasDueDate = State(initialValue: task.dueDate != nil)
        _dueDate = State(initialValue: task.dueDate ?? Date())
        // Initialize temporarySelectedDependencyIDs from current task's dependencies
        if let currentDependencyIDs = task.dependencies?.map({ $0.value }) {
            _temporarySelectedDependencyIDs = State(initialValue: Set(currentDependencyIDs))
        } else {
            _temporarySelectedDependencyIDs = State(initialValue: Set<UUID>())
        }
        // Initialize temporarySelectedTagNames from current task's tags
        _temporarySelectedTagNames = State(initialValue: Set(task.tags.map { $0.name }))
    }

    var body: some View {
        NavigationView {
            Form {
                Section("Task Details") {
                    TextField("Task Name", text: $editedText)
                        .onChange(of: editedText) { oldValue, newValue in
                             task.text = newValue // Update the bound task's text
                        }
                }
                
                Section("Due Date") {
                    Toggle("Set Due Date", isOn: $hasDueDate)
                        .tint(AppTheme.accentColor(for: colorScheme))
                    
                    if hasDueDate {
                        DatePicker("Due Date",
                                 selection: $dueDate,
                                 in: Date()...,
                                 displayedComponents: [.date, .hourAndMinute])
                            .datePickerStyle(.compact)
                    }
                }

                Section("Tags") {
                    if task.tags.isEmpty {
                        Text("No tags yet.")
                            .foregroundColor(.gray)
                    } else {
                        ForEach(task.tags) { tagItem in
                            Text(tagItem.name)
                        }
                        .onDelete(perform: deleteTaskTag)
                    }

                    HStack {
                        TextField("Add new tag", text: $newTagInput)
                        Button(action: {
                            addTag()
                        }) {
                            Image(systemName: "plus.circle.fill")
                        }
                        .disabled(newTagInput.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                    }
                    Button("Select Existing Tags") {
                        // temporarySelectedTagNames is already initialized with current task's tags
                        showingTagSelector = true
                    }
                }

                Section("Dependencies") {
                    if temporarySelectedDependencyIDs.isEmpty {
                        Text("None")
                            .foregroundColor(.gray)
                    } else {
                        ForEach(Array(temporarySelectedDependencyIDs), id: \.self) { depID in
                            if let dependentTask = allTasks.first(where: { $0.id == depID }) {
                                Text(dependentTask.text)
                            } else {
                                Text("Unknown task (ID: \(depID.uuidString.prefix(8)))")
                                    .foregroundColor(.gray)
                            }
                        }
                    }

                    Button("Select Dependencies") {
                        // temporarySelectedDependencyIDs is already initialized from task.dependencies
                        showingDependencySelector = true
                    }
                }
            }
            .navigationTitle("Edit Task")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        // Optionally revert changes if not auto-saving
                        // For now, direct binding means changes are already made.
                        // If you use @State for all fields, revert here.
                        dismiss()
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        // Apply changes from @State vars to task if not directly bound
                        task.text = editedText // Ensure this is set if using @State for editedText
                        task.dueDate = hasDueDate ? dueDate : nil
                        
                        // Update notification if due date changed
                        NotificationManager.shared.updateNotification(for: task)

                        // SwiftData automatically saves changes to @Bindable objects
                        // when the context is saved, or sometimes immediately.
                        // Explicit save can be good practice if needed.
                        try? modelContext.save()
                        dismiss()
                    }
                }
            }
            .sheet(isPresented: $showingDependencySelector, onDismiss: {
                // Update the actual task's dependencies when the sheet is dismissed
                task.dependencies = temporarySelectedDependencyIDs.map { DependencyID(value: $0) }
            }) {
                // Present the actual selection view
                TaskDependencySelectionView(
                    currentItem: task,
                    allTasks: allTasks,
                    selectedDependencyIDs: $temporarySelectedDependencyIDs
                )
                // Pass the model context if needed by the sheet content,
                // though @Environment should pick it up.
                // .environment(\.modelContext, modelContext)
            }
            .sheet(isPresented: $showingTagSelector, onDismiss: {
                // Update the actual task's tags when the sheet is dismissed
                // Preserve tags that were not part of allAvailableTagNames (e.g. newly typed ones not yet in other tasks)
                // This logic ensures that we only map from selected *existing* unique names,
                // and then add any tags that were on the task but perhaps not in the global list.
                // A more robust way might be to merge, but this keeps selected existing ones.
                
                // Get current tags that are in the selection
                var newTags = temporarySelectedTagNames.map { TagItem(name: $0) }
                
                // Add back any tags that were on the task originally and are still in temporarySelectedTagNames
                // This handles the case where a tag was on the task, deselected, then reselected.
                // Or, more simply, just rebuild from temporarySelectedTagNames.
                // The `TagItem` struct has `id` based on `name`, so duplicates by name are implicitly handled by Set.
                task.tags = temporarySelectedTagNames.map { TagItem(name: $0) }

            }) {
                ProjectTagMultiSelectionView( // Updated to new name
                    allAvailableTagNames: allAvailableTagNames,
                    selectedTagNames: $temporarySelectedTagNames
                )
            }
        }
    }

    private func deleteTaskTag(at offsets: IndexSet) {
        task.tags.remove(atOffsets: offsets)
    }

    private func addTag() {
        let tagName = newTagInput.trimmingCharacters(in: .whitespacesAndNewlines)
        if !tagName.isEmpty {
            // Check if tag already exists to avoid duplicates
            if !task.tags.contains(where: { $0.name == tagName }) {
                let newTag = TagItem(name: tagName)
                task.tags.append(newTag)
            }
            newTagInput = "" // Clear input field
        }
    }
}

#Preview {
    // Create a dummy task for the preview
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(for: Task.self, configurations: config)
    let sampleTask = Task(text: "Sample Preview Task")
    container.mainContext.insert(sampleTask)

    return TaskEditView(task: sampleTask)
        .modelContainer(container)
}