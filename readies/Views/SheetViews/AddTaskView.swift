import SwiftUI
import SwiftData

// MARK: - Add Task View, TagSelectionView, DependencySelectionView

struct AddTaskView: View {
    let allTasks: [Task]
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Environment(\.colorScheme) private var colorScheme

    @State private var taskText = ""
    @State private var selectedTags: Set<String> = [] // Stays as Set<String> for UI state
    @State private var selectedDependencies: Set<UUID> = [] // Stays as Set<UUID> for UI state
    @State private var showingTagSelection = false
    @State private var showingDependencySelection = false
    @State private var hasDueDate = false
    @State private var dueDate = Date()
    @FocusState private var isTaskTextFocused: Bool

    // Updated to work with [TagItem] and provide [String] for TagSelectionView
    var availableTagStrings: [String] {
        let allTagItems = allTasks.flatMap(\.tags)
        return Array(Set(allTagItems.map(\.name))).sorted()
    }

    var body: some View {
        NavigationView {
            Form {
                Section("Task Description") {
                    TextField("Enter task description...", text: $taskText, axis: .vertical)
                        .lineLimit(3...)
                        .focused($isTaskTextFocused)
                        .listRowBackground(AppTheme.secondaryBackgroundColor(for: colorScheme))
                }

                Section {
                    Button { showingTagSelection = true } label: {
                        HStack {
                            Text("Tags").foregroundColor(AppTheme.primaryTextColor(for: colorScheme))
                            Spacer()
                            Text(selectedTags.isEmpty ? "None" : "\(selectedTags.count) selected")
                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            Image(systemName: "chevron.right").foregroundColor(AppTheme.secondaryTextColor(for: colorScheme).opacity(0.7))
                        }
                    }
                    Button { showingDependencySelection = true } label: {
                        HStack {
                            Text("Dependencies").foregroundColor(AppTheme.primaryTextColor(for: colorScheme))
                            Spacer()
                            Text(selectedDependencies.isEmpty ? "None" : "\(selectedDependencies.count) selected")
                                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            Image(systemName: "chevron.right").foregroundColor(AppTheme.secondaryTextColor(for: colorScheme).opacity(0.7))
                        }
                    }
                }
                .listRowBackground(AppTheme.secondaryBackgroundColor(for: colorScheme))
                
                Section("Due Date") {
                    Toggle("Set Due Date", isOn: $hasDueDate)
                        .tint(AppTheme.accentColor(for: colorScheme))
                    
                    if hasDueDate {
                        DatePicker("Due Date",
                                 selection: $dueDate,
                                 in: Date()...,
                                 displayedComponents: [.date, .hourAndMinute])
                            .datePickerStyle(.compact)
                    }
                }
                .listRowBackground(AppTheme.secondaryBackgroundColor(for: colorScheme))
            }
            .onAppear { DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { isTaskTextFocused = true } }
            .navigationTitle("New Task").navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) { Button("Cancel") { dismiss() } }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Add Task") { addTask() }
                        .disabled(taskText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
            .sheet(isPresented: $showingTagSelection) { TagSelectionView(allProjectTagStrings: availableTagStrings, selectedTagStrings: $selectedTags) }
            .sheet(isPresented: $showingDependencySelection) {
                DependencySelectionView(availableTasks: allTasks.filter { !$0.completed }, selectedDependencies: $selectedDependencies)
            }
            .scrollContentBackground(.hidden) // Required for Form background color
            .background(AppTheme.primaryBackgroundColor(for: colorScheme).ignoresSafeArea())
        }
    }
}

struct TagSelectionView: View {
    // Takes [String] for allProjectTagStrings and Set<String> for selectedTagStrings
    let allProjectTagStrings: [String]
    @Binding var selectedTagStrings: Set<String>
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    @State private var newTagText = ""
    @FocusState private var isNewTagFocused: Bool

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Add new tag section
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Add New Tag")
                            .font(.headline)
                            .foregroundColor(AppTheme.primaryTextColor(for: colorScheme))
                        
                        HStack {
                            TextField("Tag name", text: $newTagText)
                                .focused($isNewTagFocused)
                                .onSubmit { addNewTag() }
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                            
                            Button("Add") { addNewTag() }
                                .disabled(newTagText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                                .buttonStyle(.borderedProminent)
                        }
                    }
                    .padding()
                    .background(AppTheme.secondaryBackgroundColor(for: colorScheme))
                    .cornerRadius(12)
                    
                    // All tags section
                    if !allProjectTagStrings.isEmpty || !selectedTagStrings.isEmpty {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Tags")
                                .font(.headline)
                                .foregroundColor(AppTheme.primaryTextColor(for: colorScheme))
                            
                            FlowLayout(spacing: 8) { // Assuming FlowLayout is available
                                // Combine all unique tag strings
                                let combinedTagStrings = Array(Set(allProjectTagStrings + Array(selectedTagStrings)))
                                ForEach(combinedTagStrings.sorted { tagSortOrder(lhs: $0, rhs: $1) }, id: \.self) { tagName in
                                    tagChip(tagName: tagName)
                                }
                            }
                        }
                        .padding()
                        .background(AppTheme.secondaryBackgroundColor(for: colorScheme))
                        .cornerRadius(12)
                    }
                }
                .padding()
            }
            .background(AppTheme.primaryBackgroundColor(for: colorScheme).ignoresSafeArea())
            .navigationTitle("Select Tags").navigationBarTitleDisplayMode(.inline)
            .toolbar { ToolbarItem(placement: .navigationBarTrailing) { Button("Done") { dismiss() } } }
            .onAppear {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    isNewTagFocused = true
                }
            }
        }
    }

    // Sorts based on selectedTagStrings (Set<String>)
    private func tagSortOrder(lhs: String, rhs: String) -> Bool {
        let lhsSelected = selectedTagStrings.contains(lhs)
        let rhsSelected = selectedTagStrings.contains(rhs)
        if lhsSelected && !rhsSelected { return true }
        if !lhsSelected && rhsSelected { return false }
        return lhs < rhs
    }
    
    @ViewBuilder
    private func tagChip(tagName: String) -> some View {
        let isSelected = selectedTagStrings.contains(tagName)
        Text(tagName)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(isSelected ? .white : AppTheme.primaryTextColor(for: colorScheme))
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(isSelected ? AppTheme.accentColor(for: colorScheme) : AppTheme.tertiaryBackgroundColor(for: colorScheme))
            .cornerRadius(20)
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(isSelected ? Color.clear : AppTheme.accentColor(for: colorScheme).opacity(0.3), lineWidth: 1)
            )
            .onTapGesture {
                withAnimation(.spring(response: 0.3)) {
                    if isSelected {
                        _ = selectedTagStrings.remove(tagName)
                    } else {
                        _ = selectedTagStrings.insert(tagName)
                    }
                }
            }
    }
    
    private func addNewTag() {
        let trimmedTag = newTagText.trimmingCharacters(in: .whitespacesAndNewlines)
        // Ensure new tag is not empty and not already in selectedTagStrings
        guard !trimmedTag.isEmpty && !selectedTagStrings.contains(trimmedTag) else {
            if selectedTagStrings.contains(trimmedTag) {
                // newTagText = "" // Optionally clear
            }
            return
        }
        
        withAnimation(.spring(response: 0.3)) {
            _ = selectedTagStrings.insert(trimmedTag)
            newTagText = "" // Clear text field after adding
        }
    }
}

struct DependencySelectionView: View {
    let availableTasks: [Task]
    @Binding var selectedDependencies: Set<UUID>
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        NavigationView {
            List {
                if availableTasks.isEmpty {
                    emptyStateSection
                } else {
                    selectedDependenciesSection
                    availableTasksSection
                }
            }
            .scrollContentBackground(.hidden)
            .background(AppTheme.primaryBackgroundColor(for: colorScheme).ignoresSafeArea())
            .navigationTitle("Select Dependencies").navigationBarTitleDisplayMode(.inline)
            .toolbar { ToolbarItem(placement: .navigationBarTrailing) { Button("Done") { dismiss() } } }
        }
    }
    
    @ViewBuilder
    private var emptyStateSection: some View {
        Section {
            Text("No available tasks to depend on.")
                .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                .padding()
        }
        .listRowBackground(AppTheme.secondaryBackgroundColor(for: colorScheme))
    }
    
    @ViewBuilder
    private var selectedDependenciesSection: some View {
        if !selectedDependencies.isEmpty {
            Section("Selected Dependencies") {
                let selectedTasks = availableTasks.filter { selectedDependencies.contains($0.id) }
                ForEach(selectedTasks) { task in
                    selectedDependencyRow(task: task)
                }
            }
            .listRowBackground(AppTheme.secondaryBackgroundColor(for: colorScheme))
        }
    }
    
    @ViewBuilder
    private var availableTasksSection: some View {
        Section("Available Tasks") {
            let availableTasksFiltered = availableTasks.filter { !selectedDependencies.contains($0.id) }
            ForEach(availableTasksFiltered) { task in
                availableTaskRow(task: task)
            }
        }
        .listRowBackground(AppTheme.secondaryBackgroundColor(for: colorScheme))
    }
    
    @ViewBuilder
    private func selectedDependencyRow(task: Task) -> some View {
        HStack {
            taskInfoView(task: task)
            Spacer()
            Button("Remove") {
                withAnimation {
                    _ = selectedDependencies.remove(task.id)
                }
            }
            .foregroundColor(AppTheme.deleteColor(for: colorScheme))
        }
        .padding(.vertical, 4)
    }
    
    @ViewBuilder
    private func availableTaskRow(task: Task) -> some View {
        Button(action: {
            withAnimation {
                _ = selectedDependencies.insert(task.id)
            }
        }) {
            HStack {
                taskInfoView(task: task)
                Spacer()
                Image(systemName: "plus.circle")
                    .foregroundColor(AppTheme.accentColor(for: colorScheme))
            }
            .padding(.vertical, 4)
        }
    }
    
    @ViewBuilder
    private func taskInfoView(task: Task) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(task.text)
                .foregroundColor(AppTheme.primaryTextColor(for: colorScheme))
                .lineLimit(2)
            if !task.tags.isEmpty {
                taskTagsView(tags: task.tags)
            }
        }
    }
    
    @ViewBuilder
    private func taskTagsView(tags: [TagItem]) -> some View { // Takes [TagItem]
        HStack(spacing: 4) {
            let displayTags = Array(tags.prefix(3))
            ForEach(displayTags) { tagItem in // Iterating over TagItem
                tagView(tagName: tagItem.name) // Passing TagItem's name
            }
        }
    }
    
    @ViewBuilder
    private func tagView(tagName: String) -> some View { // Takes tagName String
        Text(tagName)
            .font(.caption2)
            .padding(.horizontal, 4)
            .padding(.vertical, 2)
            .background(AppTheme.tagBackgroundColor(for: colorScheme))
            .foregroundColor(AppTheme.tagTextColor(for: colorScheme))
            .cornerRadius(4)
    }
}

extension AddTaskView {
    private func addTask() {
        let tagItems = selectedTags.map { TagItem(name: $0) }
        // Convert selectedDependencies (Set<UUID>) to [DependencyID]
        let dependencyIDItems = selectedDependencies.map { DependencyID(value: $0) }
        let newTask = Task(
            text: taskText.trimmingCharacters(in: .whitespacesAndNewlines),
            tags: tagItems,
            dependencies: dependencyIDItems.isEmpty ? nil : dependencyIDItems,
            dueDate: hasDueDate ? dueDate : nil
        )
        modelContext.insert(newTask)
        try? modelContext.save()
        
        // Schedule notification if due date is set
        if hasDueDate {
            NotificationManager.shared.scheduleNotification(for: newTask)
        }
        
        dismiss()
    }
}