//
//  ItemEditView.swift
//  readies
//
//  Created by <PERSON>oo on 2025/4/21.
//

import SwiftData // Import SwiftData to recognize the Item type
import SwiftUI

struct ItemEditView: View {
    @Bindable var item: Item
    @Environment(\.modelContext) private var modelContext // Needed for Query
    @Query private var allItems: [Item] // Query all items to select prerequisites from

    @State private var showingPrerequisiteSelector = false
    // Temporary state to hold selections while the sheet is presented
    @State private var temporarySelectedPrerequisites = Set<Item>()
    @State private var newTagInput: String = "" // State for new tag input

    var body: some View {
        // Use Form for better grouping and styling of controls
        Form {
            Section("Task Details") {
                TextField("Title", text: $item.title)
                // TextEditor for description
                TextEditor(text: Binding(
                    get: { item.desc ?? "" },
                    set: { item.desc = $0 }
                ))
                .frame(height: 100) // Set a default height for the TextEditor

                // DatePicker for optional due date
                DatePicker("Due Date", selection: Binding( // Handle optional Date binding
                    get: { item.dueDate ?? Date() }, // Provide a default if nil
                    set: { item.dueDate = $0 }
                ), displayedComponents: [.date, .hourAndMinute])
                // Add a button to clear the due date
                if item.dueDate != nil {
                    Button("Clear Due Date") { item.dueDate = nil }
                        .foregroundColor(.red)
                }
            }

            Section("Tags") {
                // Display existing tags
                if let tags = item.tags, !tags.isEmpty {
                    ForEach(tags, id: \.self) { tag in
                        Text(tag)
                    }
                } else {
                    Text("No tags yet.")
                        .foregroundColor(.gray)
                }

                // Simple TextField to add a new tag
                // For a more robust solution, consider a dedicated tag input view
                HStack {
                    TextField("Add new tag", text: $newTagInput)
                    Button(action: {
                        if !newTagInput.isEmpty {
                            if item.tags == nil {
                                item.tags = []
                            }
                            item.tags?.append(newTagInput)
                            newTagInput = "" // Clear input field
                        }
                    }) {
                        Image(systemName: "plus.circle.fill")
                    }
                }
            }

            Section("Prerequisites") {
                // List current prerequisites
                if let prerequisites = item.prerequisites, !prerequisites.isEmpty {
                    ForEach(prerequisites) { prereq in
                        Text(prereq.title)
                    }
                } else {
                    Text("None")
                        .foregroundColor(.gray)
                }

                Button("Select Prerequisites") {
                    // Initialize temporary set from current prerequisites before showing sheet
                    temporarySelectedPrerequisites = Set(item.prerequisites ?? [])
                    showingPrerequisiteSelector = true
                }
            }

            Section("Metadata") {
                // Picker for priority
                Picker("Priority", selection: $item.priority) {
                    ForEach(0 ..< 4) { priorityLevel in
                        Text(priorityLabel(for: priorityLevel)).tag(priorityLevel)
                    }
                }

                // Display creation date (read-only)
                Text("Created: \(item.timestamp, format: .dateTime)")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
        }
        // .padding() // Form adds its own padding, remove this
        .navigationTitle("Edit Item")
        #if os(iOS)
            .navigationBarTitleDisplayMode(.inline)
        #endif
            .sheet(isPresented: $showingPrerequisiteSelector, onDismiss: {
                // Update the actual item's prerequisites when the sheet is dismissed
                // Convert Set back to Array for the relationship
                item.prerequisites = Array(temporarySelectedPrerequisites)
            }) {
                // Present the actual selection view
                PrerequisiteSelectionView(
                    currentItem: item,
                    selectedPrerequisites: $temporarySelectedPrerequisites
                )
                // Pass the model context if needed by the sheet content (optional here, but good practice)
                // .environment(\.modelContext, modelContext)
            }
    }

    // Helper function to get priority label
    private func priorityLabel(for level: Int) -> String {
        switch level {
        case 1: return "Low"
        case 2: return "Medium"
        case 3: return "High"
        default: return "None"
        }
    }
}

// Restore and update Preview provider
#Preview {
    // Attempting a very simple preview to isolate issues
    // If 'Item' cannot be found here, it's likely an Xcode build/indexing issue.
    // This preview will likely fail if Item is not found, but it simplifies the structure.
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    guard let container = try? ModelContainer(for: Item.self, configurations: config) else {
        return Text("Error: Could not create ModelContainer for Preview") // Return a View
    }
    let sampleItem = Item(title: "Preview Task")
    container.mainContext.insert(sampleItem) // Insert into context for the view

    return ItemEditView(item: sampleItem) // Return a View
        .modelContainer(container)
}

// #Preview {
//    ContentView()
//        .modelContainer(for: Item.self, inMemory: true)
// }
