//
//  PrerequisiteSelectionView.swift
//  readies
//
//  Created by Roo on 2025/4/21.
//

import SwiftUI
import SwiftData

struct PrerequisiteSelectionView: View {
    @Environment(\.dismiss) var dismiss
    @Environment(\.modelContext) private var modelContext

    // The item we are editing prerequisites for
    var currentItem: Item

    // Binding to the set of selected prerequisites (passed from ItemEditView)
    @Binding var selectedPrerequisites: Set<Item>

    // Query all items to display potential prerequisites
    @Query private var allItems: [Item]

    // Filtered list of items that can be prerequisites
    private var availablePrerequisites: [Item] {
        allItems.filter { item in
            // Exclude the current item itself
            item.persistentModelID != currentItem.persistentModelID &&
            // Exclude items that already depend on the current item (prevent cycles)
            !(item.prerequisites?.contains(where: { $0.persistentModelID == currentItem.persistentModelID }) ?? false)
        }
    }

    var body: some View {
        NavigationView {
            List {
                ForEach(availablePrerequisites) { item in
                    HStack {
                        Text(item.title)
                        Spacer()
                        if selectedPrerequisites.contains(item) {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.blue)
                        } else {
                            Image(systemName: "circle")
                                .foregroundColor(.gray)
                        }
                    }
                    .contentShape(Rectangle()) // Make the whole row tappable
                    .onTapGesture {
                        toggleSelection(item: item)
                    }
                }
            }
            .navigationTitle("Select Prerequisites")
            #if os(iOS)
            .navigationBarTitleDisplayMode(.inline)
            #endif
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss() // Dismiss without saving changes to the binding
                    }
                }
                ToolbarItem(placement: .confirmationAction) {
                    Button("Done") {
                        // The binding is already updated by toggleSelection
                        dismiss()
                    }
                }
            }
        }
    }

    // Function to add/remove item from the selection set
    private func toggleSelection(item: Item) {
        if selectedPrerequisites.contains(item) {
            selectedPrerequisites.remove(item)
        } else {
            selectedPrerequisites.insert(item)
        }
    }
}

// Preview requires setup similar to ItemEditView
#Preview {
    // Need a sample Item and a binding for the preview
    // This setup is complex due to SwiftData and Bindings in previews.
    // For simplicity, we'll just show a placeholder text in the preview.
    Text("Prerequisite Selection Preview Placeholder")

    // Full preview setup (optional, can be complex):
    /*
    struct PreviewWrapper: View {
        @State var selected = Set<Item>()
        var sampleItem: Item

        init() {
            // Create a sample item within a temporary container
            let config = ModelConfiguration(isStoredInMemoryOnly: true)
            let container = try! ModelContainer(for: Item.self, configurations: config)
            let item1 = Item(title: "Task 1")
            let item2 = Item(title: "Task 2")
            let item3 = Item(title: "Task 3 - Current")
            container.mainContext.insert(item1)
            container.mainContext.insert(item2)
            container.mainContext.insert(item3)
            self.sampleItem = item3
            self.selected = [item1] // Pre-select one
        }

        var body: some View {
            PrerequisiteSelectionView(currentItem: sampleItem, selectedPrerequisites: $selected)
                .modelContainer(for: Item.self, inMemory: true) // Provide container
        }
    }
    return PreviewWrapper()
    */
}