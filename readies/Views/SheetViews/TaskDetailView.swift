import SwiftUI
import SwiftData

// MARK: - Task Detail View

struct TaskDetailView: View {
    let task: Task
    let allTasks: [Task] // Used by isCompletable and to find dependent/dependency tasks
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @State private var showingEditSheet = false // Added for presenting edit view

    var dependentTasks: [Task] {
        allTasks.filter { aTaskInAll in
            guard let dependencies = aTaskInAll.dependencies else { return false }
            return dependencies.contains(where: { $0.value == task.id })
        }
    }

    var dependencyTasks: [Task] {
        guard let dependencies = task.dependencies else { return [] }
        return dependencies.compactMap { depIDItem in
            allTasks.first { $0.id == depIDItem.value }
        }
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Task Info Section
                    VStack(alignment: .leading, spacing: 12) {
                        Text(task.text).font(.title2.weight(.semibold))
                            .foregroundColor(AppTheme.primaryTextColor(for: colorScheme))

                        HStack {
                            Text("Status:").foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            Text(task.completed ? "Completed" : (isCompletable(task) ? "Ready" : "Blocked"))
                                .fontWeight(.medium)
                                .foregroundColor(task.completed ? AppTheme.completedColor(for: colorScheme) : (isCompletable(task) ? AppTheme.readyColor(for: colorScheme) : AppTheme.blockedColor(for: colorScheme)))
                            Spacer()
                        }
                        HStack {
                            Text("Created:").foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            Text(task.createdAt, style: .date)
                            Spacer()
                        }.font(.subheadline).foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))

                        if !task.tags.isEmpty {
                            Text("Tags").font(.headline).foregroundColor(AppTheme.primaryTextColor(for: colorScheme))
                            FlowLayout(spacing: 8) { // Assuming FlowLayout is available globally or imported
                                ForEach(task.tags) { tagItem in // Iterating over TagItem
                                    Text(tagItem.name).font(.caption.weight(.medium)) // Displaying TagItem's name
                                        .padding(.horizontal, 8).padding(.vertical, 4)
                                        .background(AppTheme.tagBackgroundColor(for: colorScheme)).cornerRadius(8)
                                        .foregroundColor(AppTheme.tagTextColor(for: colorScheme))
                                }
                            }
                        }
                    }
                    .padding().background(AppTheme.secondaryBackgroundColor(for: colorScheme)).cornerRadius(12)

                    if !dependencyTasks.isEmpty {
                        DependencyListSection(title: "Depends On", tasks: dependencyTasks, colorScheme: colorScheme)
                    }
                    if !dependentTasks.isEmpty {
                        DependencyListSection(title: "Blocks These Tasks", tasks: dependentTasks, isBlocking: true, colorScheme: colorScheme)
                    }
                }
                .padding()
            }
            .background(AppTheme.primaryBackgroundColor(for: colorScheme).ignoresSafeArea())
            .navigationTitle("Task Details").navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Edit") {
                        showingEditSheet = true
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .sheet(isPresented: $showingEditSheet) {
                // Present the TaskEditView
                TaskEditView(task: task)
                    // Pass the model context if TaskEditView needs it explicitly,
                    // though @Environment should pick it up.
                    // .environment(\.modelContext, modelContext)
            }
        }
    }
}

struct DependencyListSection: View {
    let title: String
    let tasks: [Task]
    var isBlocking: Bool = false
    let colorScheme: ColorScheme

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title).font(.headline).foregroundColor(AppTheme.primaryTextColor(for: colorScheme))
            ForEach(tasks) { depTask in
                HStack {
                    Image(systemName: depTask.completed ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(depTask.completed ? AppTheme.completedColor(for: colorScheme) : (isBlocking ? AppTheme.secondaryTextColor(for: colorScheme) : AppTheme.blockedColor(for: colorScheme)))
                    Text(depTask.text)
                        .strikethrough(depTask.completed)
                        .foregroundColor(depTask.completed ? AppTheme.secondaryTextColor(for: colorScheme) : AppTheme.primaryTextColor(for: colorScheme))
                    Spacer()
                }
                .padding(10).background(AppTheme.tertiaryBackgroundColor(for: colorScheme)).cornerRadius(8)
            }
        }
        .padding().background(AppTheme.secondaryBackgroundColor(for: colorScheme)).cornerRadius(12)
    }
}

extension TaskDetailView {
    private func isCompletable(_ task: Task) -> Bool { // Takes the task explicitly
        if task.completed { return false }
        guard let dependencies = task.dependencies else { return true } // No dependencies means completable
        return dependencies.allSatisfy { depIDItem in
            self.allTasks.first { $0.id == depIDItem.value }?.completed ?? false
        }
    }
}