import SwiftUI
import SwiftData

struct TaskDependencySelectionView: View {
    let currentItem: readies.Task
    let allTasks: [readies.Task] // All tasks to select from
    @Binding var selectedDependencyIDs: Set<UUID> // Binding to the set of selected dependency IDs

    // Filtered list of tasks that can be chosen as dependencies
    // This logic remains specific to dependency selection.
    var selectableTasks: [readies.Task] {
        allTasks.filter { task in
            // Cannot depend on itself
            task.id != currentItem.id &&
            // Basic check: Don't allow selecting a task that already depends on the current item
            // (This is a simple check, not full cycle detection)
            !(task.dependencies?.contains(where: { $0.value == currentItem.id }) ?? false)
        }.sorted { $0.text < $1.text } // Sort for consistent display
    }

    var body: some View {
        MultiSelectionListView(
            allItems: selectableTasks, // Use the filtered and sorted list
            selectedItemIDs: $selectedDependencyIDs,
            labelContent: { task in
                Text(task.text) // Display the task's text
            },
            navigationTitle: "Select Dependencies"
        )
    }
}

// Preview requires a bit more setup due to the bindings and data
#Preview {
    // It's often cleaner to encapsulate preview setup if it's complex,
    // but for this, we can do it directly ensuring the View is the last expression.
    
    // Define a state variable at a scope where it can be used by the View.
    // The @State property wrapper needs to be part of a View struct or PreviewProvider.
    // A simple way for previews is to wrap it in a small helper View or use a static var in PreviewProvider.
    // For simplicity here, we'll make a small struct that holds the state.

    struct PreviewWrapper: View {
        @State var previewSelectedIDs = Set<UUID>()
        let container: ModelContainer
        let task1: readies.Task
        let task2: readies.Task
        let task3: readies.Task
        let task4: readies.Task

        init() {
            let config = ModelConfiguration(isStoredInMemoryOnly: true)
            container = try! ModelContainer(for: readies.Task.self, configurations: config)

            task1 = readies.Task(text: "Task 1")
            task2 = readies.Task(text: "Task 2 - Depends on Task 1")
            task3 = readies.Task(text: "Task 3 - Current Item")
            task4 = readies.Task(text: "Task 4 - Independent")
            
            container.mainContext.insert(task1)
            container.mainContext.insert(task2)
            container.mainContext.insert(task3)
            container.mainContext.insert(task4)

            task2.dependencies = [DependencyID(value: task1.id)]
            // Initialize the state based on task3's initial dependencies for the preview
             _previewSelectedIDs = State(initialValue: Set([task1.id])) // Task 3 initially depends on Task 1
        }

        var body: some View {
            TaskDependencySelectionView(
                currentItem: task3,
                allTasks: [task1, task2, task3, task4],
                selectedDependencyIDs: $previewSelectedIDs
            )
            .modelContainer(container)
        }
    }
    
    return PreviewWrapper()
}