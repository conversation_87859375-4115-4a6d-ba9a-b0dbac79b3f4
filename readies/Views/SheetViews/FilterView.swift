import SwiftUI

// MARK: - Filter View

struct FilterView: View {
    let allTags: [String]
    @Binding var selectedTags: Set<String>
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    if allTags.isEmpty {
                        Text("No tags available to filter by.")
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            .padding()
                            .frame(maxWidth: .infinity, alignment: .center)
                    } else {
                        HStack {
                            Text("Filter by Tags")
                                .font(.title2.weight(.semibold))
                                .foregroundColor(AppTheme.primaryTextColor(for: colorScheme))
                            Spacer()
                            if !selectedTags.isEmpty {
                                Button("Clear All") { withAnimation { selectedTags.removeAll() } }
                                    .foregroundColor(AppTheme.deleteColor(for: colorScheme))
                            }
                        }
                        Text("Tasks must have ALL selected tags.")
                            .font(.caption)
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))

                        FlowLayout(spacing: 8) { // Assuming FlowLayout is available
                            ForEach(allTags.sorted { tagSortOrder(lhs: $0, rhs: $1) }, id: \.self) { tag in
                                tagChip(tag: tag)
                            }
                        }
                    }
                }
                .padding()
            }
            .background(AppTheme.primaryBackgroundColor(for: colorScheme).ignoresSafeArea())
            .navigationTitle("Filters").navigationBarTitleDisplayMode(.inline)
            .toolbar { ToolbarItem(placement: .navigationBarTrailing) { Button("Done") { dismiss() } } }
        }
    }

    private func tagSortOrder(lhs: String, rhs: String) -> Bool {
        let lhsSelected = selectedTags.contains(lhs)
        let rhsSelected = selectedTags.contains(rhs)
        if lhsSelected && !rhsSelected { return true }
        if !lhsSelected && rhsSelected { return false }
        return lhs < rhs
    }

    @ViewBuilder
    private func tagChip(tag: String) -> some View {
        let isSelected = selectedTags.contains(tag)
        Text(tag)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(isSelected ? .white : AppTheme.primaryTextColor(for: colorScheme))
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(isSelected ? AppTheme.accentColor(for: colorScheme) : AppTheme.tertiaryBackgroundColor(for: colorScheme))
            .cornerRadius(20)
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(isSelected ? Color.clear : AppTheme.accentColor(for: colorScheme).opacity(0.3), lineWidth: 1)
            )
            .onTapGesture {
                withAnimation(.spring(response: 0.3)) {
                    if isSelected {
                        _ = selectedTags.remove(tag)
                    } else {
                        _ = selectedTags.insert(tag)
                    }
                }
            }
    }
}