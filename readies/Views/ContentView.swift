import SwiftData
import SwiftUI

// MARK: - Main Content View

struct ContentView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.colorScheme) private var colorScheme
    @Query private var tasks: [readies.Task] // Qualified Task
    @State private var showingAddTask = false
    @State private var showingFilters = false
    @State private var selectedFilterTags: Set<String> = []
    @State private var showCompletableOnly = false
    @State private var showingCompletedTasks = false
    @State private var selectedTask: readies.Task? // Qualified Task
    @StateObject private var notificationManager = NotificationManager.shared

    // Updated to work with [TagItem]
    var allTags: [TagItem] {
        let allTagItems = tasks.flatMap(\.tags)
        // Create a Set of names to get unique tag names, then map back to TagItem
        // This assumes you want unique TagItems based on their names.
        // If TagItems can be non-unique even with the same name, adjust logic.
        let uniqueTagNames = Set(allTagItems.map(\.name))
        return uniqueTagNames.map { TagItem(name: $0) }.sorted { $0.name < $1.name }
    }

    var allTagStrings: [String] { // Helper for views that still need [String] for selection sets
        allTags.map(\.name).sorted()
    }

    var filteredAndSortedTasks: [readies.Task] { // Qualified Task
        var filtered = tasks

        // Hide completed tasks older than 1 day unless explicitly showing completed tasks
        if !showingCompletedTasks {
            let oneDayAgo = Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date()
            filtered = filtered.filter { task in
                !task.completed || (task.completedAt ?? task.createdAt) > oneDayAgo
            }
        }

        if !selectedFilterTags.isEmpty {
            // Filter by checking if task.tags (which is [TagItem]) contains any TagItem whose name is in selectedFilterTags (which is Set<String>)
            filtered = filtered.filter { task in
                selectedFilterTags.allSatisfy { selectedTagName in
                    task.tags.contains(where: { $0.name == selectedTagName })
                }
            }
        }
        if showCompletableOnly {
            filtered = filtered.filter { isCompletable($0) }
        }
        return filtered.sorted { a, b in
            if !a.completed && b.completed { return true }
            if a.completed && !b.completed { return false }
            if a.completed { return a.createdAt > b.createdAt }
            let aCompletable = isCompletable(a)
            let bCompletable = isCompletable(b)
            if aCompletable && !bCompletable { return true }
            if !aCompletable && bCompletable { return false }
            return a.createdAt > b.createdAt
        }
    }

    var body: some View {
        NavigationView {
            ZStack {
                AppTheme.primaryBackgroundColor(for: colorScheme)
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    HStack {
                        Text("Tasks")
                            .font(.system(size: 32, weight: .bold, design: .rounded))
                            .foregroundColor(AppTheme.primaryTextColor(for: colorScheme))
                        Spacer()
                        Button(action: { withAnimation { showCompletableOnly.toggle() } }) {
                            HStack(spacing: 6) {
                                Image(systemName: showCompletableOnly ? "checkmark.circle.fill" : "circle")
                                    .foregroundColor(showCompletableOnly ? AppTheme.readyColor(for: colorScheme) : AppTheme.secondaryTextColor(for: colorScheme))
                                Text("Ready")
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(showCompletableOnly ? AppTheme.readyColor(for: colorScheme) : AppTheme.secondaryTextColor(for: colorScheme))
                            }
                            .padding(.horizontal, 10).padding(.vertical, 6)
                            .background(showCompletableOnly ? AppTheme.readyColor(for: colorScheme).opacity(0.15) : AppTheme.tertiaryBackgroundColor(for: colorScheme).opacity(0.8))
                            .cornerRadius(12)
                        }
                        if !selectedFilterTags.isEmpty {
                            HStack(spacing: 4) {
                                Image(systemName: "line.3.horizontal.decrease.circle.fill")
                                Text("\(filteredAndSortedTasks.count)")
                                    .font(.system(size: 14, weight: .semibold))
                            }
                            .font(.system(size: 16))
                            .foregroundColor(AppTheme.accentColor(for: colorScheme))
                            .padding(.horizontal, 8).padding(.vertical, 4)
                            .background(AppTheme.accentColor(for: colorScheme).opacity(0.1))
                            .cornerRadius(12)
                        }
                    }
                    .padding(.horizontal, 20).padding(.top, 4).padding(.bottom, 8)

                    ScrollView {
                        LazyVStack(spacing: 0) {
                            // Due Soon Section
                            DueSoonSection(tasks: tasks)

                            LazyVStack(spacing: 6) {
                                ForEach(filteredAndSortedTasks) { task in // task is readies.Task
                                    TaskCard(task: task, allTasks: tasks)
                                        .contextMenu { contextMenuItems(for: task) }
                                        .onTapGesture { selectedTask = task }
                                        .transition(.opacity.combined(with: .scale(scale: 0.95)))
                                }
                                if filteredAndSortedTasks.isEmpty {
                                    EmptyStateView(showCompletableOnly: showCompletableOnly, hasFilters: !selectedFilterTags.isEmpty)
                                        .padding(.top, 40)
                                }
                            }
                            .padding(.horizontal, 20).padding(.bottom, 120).padding(.top, 20)
                        }
                    }
                }

                VStack { // FABs
                    Spacer()
                    HStack {
                        Spacer()
                        VStack(spacing: 16) {
                            let fabSize: CGFloat = 56 // Standard FAB size
                            Button(action: { withAnimation { showingCompletedTasks.toggle() } }) {
                                Image(systemName: showingCompletedTasks ? "checkmark.circle.fill" : "checkmark.circle")
                                    .font(.system(size: 20, weight: .medium)).foregroundColor(.white)
                                    .frame(width: fabSize, height: fabSize)
                                    .background(Circle().fill(showingCompletedTasks ? AppTheme.completedColor(for: colorScheme) : AppTheme.inactiveFABColor(for: colorScheme)))
                            }
                            Button(action: { showingFilters = true }) {
                                Image(systemName: "line.3.horizontal.decrease")
                                    .font(.system(size: 20, weight: .medium)).foregroundColor(.white)
                                    .frame(width: fabSize, height: fabSize)
                                    .background(Circle().fill(selectedFilterTags.isEmpty ? AppTheme.inactiveFABColor(for: colorScheme) : AppTheme.accentColor(for: colorScheme)))
                            }
                            Button(action: { showingAddTask = true }) {
                                Image(systemName: "plus")
                                    .font(.system(size: 24, weight: .medium)).foregroundColor(.white)
                                    .frame(width: fabSize, height: fabSize)
                                    .background(Circle().fill(AppTheme.accentColor(for: colorScheme)))
                            }
                        }
                    }.padding(.trailing, 20).padding(.bottom, 40)
                }
            }
            .navigationBarHidden(true)
            .sheet(isPresented: $showingAddTask) { AddTaskView(allTasks: tasks) }
            .sheet(isPresented: $showingFilters) { FilterView(allTags: allTagStrings, selectedTags: $selectedFilterTags) }
            .sheet(item: $selectedTask) { task in TaskDetailView(task: task, allTasks: tasks) } // task is readies.Task
        }
        .onAppear {
            notificationManager.requestAuthorization()
            notificationManager.checkAndScheduleAllNotifications(tasks: tasks)
        }
        .onReceive(NotificationCenter.default.publisher(for: .openTaskDetail)) { notification in
            if let taskId = notification.userInfo?["taskId"] as? UUID,
               let task = tasks.first(where: { $0.id == taskId }) {
                selectedTask = task
            }
        }
    }

    @ViewBuilder
    private func contextMenuItems(for task: readies.Task) -> some View { // Qualified Task
        Button { selectedTask = task } label: { Label("View Details", systemImage: "info.circle") }
        Button { toggleTaskCompletion(task) } label: {
            Label(task.completed ? "Mark Incomplete" : "Mark Complete", systemImage: task.completed ? "xmark.circle" : "checkmark.circle")
        }.disabled(!isCompletable(task) && !task.completed)
        Divider()
        Button(role: .destructive) { deleteTask(task) } label: { Label("Delete Task", systemImage: "trash") }
    }
}

// MARK: - Preview

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
            .modelContainer(for: readies.Task.self, inMemory: true) // Qualified Task
            .preferredColorScheme(.dark) // Preview dark mode
        ContentView()
            .modelContainer(for: readies.Task.self, inMemory: true) // Qualified Task
            .preferredColorScheme(.light) // Preview light mode
    }
}

// Dummy implementations for brevity in this response
// These functions are part of ContentView and should remain here or be moved to a dedicated helper/extension file for ContentView.
extension ContentView {
    private func isCompletable(_ task: readies.Task) -> Bool { // Qualified Task
        if task.completed { return false }
        guard let dependencies = task.dependencies else { return true } // No dependencies means completable
        return dependencies.allSatisfy { depIDItem in
            self.tasks.first { $0.id == depIDItem.value }?.completed ?? false
        }
    }

    private func toggleTaskCompletion(_ task: readies.Task) { // Qualified Task
        task.completed.toggle()
        if task.completed {
            task.completedAt = Date()
        } else {
            task.completedAt = nil
        }
        try? modelContext.save()
    }

    private func deleteTask(_ task: readies.Task) { modelContext.delete(task); try? modelContext.save() } // Qualified Task
}
