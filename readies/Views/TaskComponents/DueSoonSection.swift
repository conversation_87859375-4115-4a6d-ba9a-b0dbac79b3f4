//
//  DueSoonSection.swift
//  readies
//
//  Created by <PERSON><PERSON> on 2025/6/6.
//

import SwiftData
import SwiftUI

struct DueSoonSection: View {
    let tasks: [Task]
    @Environment(\.colorScheme) private var colorScheme

    var dueSoonTasks: [Task] {
        tasks.filter { task in
            !task.completed && (task.isOverdue || task.isDueSoon)
        }.sorted { a, b in
            // Sort by due date, earliest first
            guard let aDate = a.dueDate else { return false }
            guard let bDate = b.dueDate else { return true }
            return aDate < bDate
        }
    }

    var body: some View {
        if !dueSoonTasks.isEmpty {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Image(systemName: "clock.badge.exclamationmark")
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(AppTheme.warningColor(for: colorScheme))

                    Text("Due Soon")
                        .font(.system(size: 20, weight: .bold, design: .rounded))
                        .foregroundColor(AppTheme.primaryTextColor(for: colorScheme))

                    Spacer()

                    Text("\(dueSoonTasks.count)")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(AppTheme.warningColor(for: colorScheme).opacity(0.2))
                        .cornerRadius(8)
                }
                .padding(.horizontal, 20)
                .padding(.top, 8)

                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(dueSoonTasks) { task in
                            DueSoonTaskCard(task: task)
                        }
                    }
                    .padding(.horizontal, 20)
                }
            }
            .padding(.bottom, 8)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        AppTheme.warningColor(for: colorScheme).opacity(0.05),
                        AppTheme.primaryBackgroundColor(for: colorScheme),
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
        }
    }
}

struct DueSoonTaskCard: View {
    let task: Task
    @Environment(\.colorScheme) private var colorScheme
    @State private var isPressed = false

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                if task.isOverdue {
                    Label("Overdue", systemImage: "exclamationmark.triangle.fill")
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.red)
                        .cornerRadius(6)
                } else {
                    Label("Due Soon", systemImage: "clock.fill")
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.orange)
                        .cornerRadius(6)
                }

                Spacer()
            }

            Text(task.text)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(AppTheme.primaryTextColor(for: colorScheme))
                .lineLimit(2)
                .multilineTextAlignment(.leading)

            if let formattedDate = task.formattedDueDate {
                Text(formattedDate)
                    .font(.caption)
                    .foregroundColor(task.isOverdue ? Color.red : AppTheme.secondaryTextColor(for: colorScheme))
            }

            if !task.tags.isEmpty {
                HStack(spacing: 4) {
                    ForEach(Array(task.tags.prefix(2))) { tag in
                        Text(tag.name)
                            .font(.caption2)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(AppTheme.tagBackgroundColor(for: colorScheme))
                            .foregroundColor(AppTheme.tagTextColor(for: colorScheme))
                            .cornerRadius(4)
                    }

                    if task.tags.count > 2 {
                        Text("+\(task.tags.count - 2)")
                            .font(.caption2)
                            .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                    }
                }
            }
        }
        .padding(12)
        .frame(width: 200)
        .background(AppTheme.secondaryBackgroundColor(for: colorScheme))
        .cornerRadius(12)
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .onTapGesture {
            withAnimation(.spring(response: 0.3)) {
                isPressed = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.spring(response: 0.3)) {
                    isPressed = false
                }
            }
        }
    }
}
