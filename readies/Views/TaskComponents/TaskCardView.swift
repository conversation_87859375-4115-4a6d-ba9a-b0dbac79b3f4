import SwiftData
import SwiftUI

// MARK: - Task Card View

struct TaskCard: View {
    let task: Task
    let allTasks: [Task] // Used by isCompletable
    @Environment(\.modelContext) private var modelContext
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        let isBlocked = !task.completed && !isCompletable(task)

        HStack(alignment: .center, spacing: 12) {
            // Color indicator block
            Rectangle()
                .fill(
                    task.completed ? AppTheme.completedColor(for: colorScheme) :
                        isCompletable(task) ? AppTheme.readyColor(for: colorScheme) :
                        AppTheme.blockedColor(for: colorScheme)
                )
                .frame(width: 4)
                .cornerRadius(2)

            // Toggle button - now properly centered
            <PERSON><PERSON>(action: toggleTaskCompletion) {
                ZStack {
                    Circle()
                        .fill(task.completed ? AppTheme.completedColor(for: colorScheme) : AppTheme.tertiaryBackgroundColor(for: colorScheme))
                        .frame(width: 24, height: 24)
                    if task.completed {
                        Image(systemName: "checkmark").font(.system(size: 12, weight: .bold)).foregroundColor(.white)
                    }
                }
            }
            .disabled(!isCompletable(task) && !task.completed)
            .buttonStyle(PlainButtonStyle())

            VStack(alignment: .leading, spacing: 6) {
                HStack {
                    // Task text
                    Text(task.text)
                        .font(.system(size: 15, weight: .medium))
                        .foregroundColor(task.completed ? AppTheme.secondaryTextColor(for: colorScheme) : AppTheme.primaryTextColor(for: colorScheme))
                        .strikethrough(task.completed)
                        .lineLimit(2)
                    Spacer()
                }

                // Only show the bottom row if there are tags or metadata to display
                if !task.tags.isEmpty || task.formattedDueDate != nil || isBlocked || (task.dependencies?.isEmpty == false) {
                    HStack {
                        if !task.tags.isEmpty {
                            HStack(spacing: 4) {
                                ForEach(task.tags.prefix(2)) { tagItem in
                                    Text(tagItem.name)
                                        .font(.system(size: 10, weight: .semibold))
                                        .foregroundColor(AppTheme.tagTextColor(for: colorScheme))
                                        .padding(.horizontal, 6).padding(.vertical, 3)
                                        .background(AppTheme.tagBackgroundColor(for: colorScheme))
                                        .cornerRadius(6)
                                }
                                if task.tags.count > 2 {
                                    Text("+\(task.tags.count - 2)")
                                        .font(.system(size: 10, weight: .semibold))
                                        .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                                }
                            }
                        }
                        HStack(spacing: 6) {
                            // Due date indicator
                            if let formattedDate = task.formattedDueDate, !task.completed {
                                HStack(spacing: 2) {
                                    Image(systemName: task.isOverdue ? "exclamationmark.triangle.fill" : "clock.fill")
                                        .font(.system(size: 10))
                                    Text(formattedDate)
                                        .font(.system(size: 10, weight: .medium))
                                }
                                .foregroundColor(task.isOverdue ? Color.red : (task.isDueSoon ? Color.orange : AppTheme.secondaryTextColor(for: colorScheme)))
                            }

                            if isBlocked {
                                Image(systemName: "lock.fill")
                                    .font(.system(size: 10))
                                    .foregroundColor(AppTheme.blockedColor(for: colorScheme))
                            }
                            if let deps = task.dependencies, !deps.isEmpty {
                                Text("\(deps.count)")
                                    .font(.system(size: 10, weight: .medium))
                                    .foregroundColor(AppTheme.secondaryTextColor(for: colorScheme))
                            }
                        }
                    }
                }
            }
        }
        .padding(12)
        .frame(minHeight: 70)
        .background(AppTheme.secondaryBackgroundColor(for: colorScheme))
        .cornerRadius(12)
        .padding(.leading, isBlocked ? 20 : 0) // Indent blocked tasks
    }
}

extension TaskCard {
    private func toggleTaskCompletion() {
        task.completed.toggle()
        if task.completed {
            task.completedAt = Date()
            // Cancel notification when task is completed
            NotificationManager.shared.cancelNotification(for: task.id)
        } else {
            task.completedAt = nil
            // Reschedule notification if task has due date
            if task.dueDate != nil {
                NotificationManager.shared.scheduleNotification(for: task)
            }
        }
        // Assuming modelContext is accessible here, if not, it needs to be passed or obtained differently.
        // For this refactor, we'll assume it's available as it was in the original structure.
        try? modelContext.save()
    }

    private func isCompletable(_ taskForCheck: Task) -> Bool { // Parameter to avoid confusion with self.task
        if taskForCheck.completed { return false }
        guard let dependencies = taskForCheck.dependencies else { return true } // No dependencies means completable
        return dependencies.allSatisfy { depIDItem in
            allTasks.first { $0.id == depIDItem.value }?.completed ?? false
        }
    }
}
