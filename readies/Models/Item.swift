//
//  Item.swift
//  readies
//
//  Created by <PERSON><PERSON> on 2025/4/21.
//

import Foundation
import SwiftData

@Model
public final class Item { // Add public keyword
    public var title: String
    public var isCompleted: Bool
    public var timestamp: Date
    public var dueDate: Date?
    public var priority: Int
    public var desc: String? // Renamed from desc
    public var tags: [String]? // Added tags

    // Relationships for Dependencies
    // Inverse relationship: If Item A is a prerequisite for Item B, then Item B is a dependent of Item A.
    @Relationship(inverse: \Item.prerequisites) public var dependents: [Item]? = [] // Items that depend on this one
    public var prerequisites: [Item]? = [] // Items that must be completed before this one

    init(title: String = "", description: String? = nil, tags: [String]? = nil, isCompleted: Bool = false, timestamp: Date = .now, dueDate: Date? = nil, priority: Int = 0, prerequisites: [Item]? = nil) { // Add description, tags and prerequisites to init
        self.title = title
        desc = description
        self.tags = tags
        self.isCompleted = isCompleted
        self.timestamp = timestamp
        self.dueDate = dueDate
        self.priority = priority
        // Note: We initialize dependents as empty. SwiftData manages the inverse relationship.
        // We only need to explicitly set prerequisites when creating/editing.
        self.prerequisites = prerequisites ?? []
    }
}
