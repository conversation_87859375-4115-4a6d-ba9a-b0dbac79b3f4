//
//  Task.swift
//  readies
//
//  Created by <PERSON><PERSON> on 2025/6/4.
//

import SwiftData
import SwiftUI

@Model
class Task {
    @Attribute(.unique) var id: UUID
    var text: String
    var completed: Bool
    var tags: [TagItem]
    var dependencies: [DependencyID]?
    var completedAt: Date?
    var createdAt: Date
    
    // New due date properties
    var dueDate: Date?
    var hasNotified: Bool = false
    
    init(text: String, 
         completed: Bool = false, 
         tags: [TagItem] = [], 
         dependencies: [DependencyID]? = nil, 
         completedAt: Date? = nil,
         dueDate: Date? = nil) {
        id = UUID()
        self.text = text
        self.completed = completed
        self.tags = tags
        self.dependencies = dependencies
        self.createdAt = Date()
        self.completedAt = completedAt
        self.dueDate = dueDate
        self.hasNotified = false
    }
    
    // Computed properties for due date status
    var isOverdue: Bool {
        guard let dueDate = dueDate, !completed else { return false }
        return dueDate < Date()
    }
    
    var isDueSoon: Bool {
        guard let dueDate = dueDate, !completed, !isOverdue else { return false }
        let hoursUntilDue = dueDate.timeIntervalSinceNow / 3600
        return hoursUntilDue <= 24 // Due within 24 hours
    }
    
    var daysUntilDue: Int? {
        guard let dueDate = dueDate else { return nil }
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: Date(), to: dueDate)
        return components.day
    }
    
    var formattedDueDate: String? {
        guard let dueDate = dueDate else { return nil }
        let formatter = DateFormatter()
        
        if Calendar.current.isDateInToday(dueDate) {
            formatter.dateFormat = "'Today at' h:mm a"
        } else if Calendar.current.isDateInTomorrow(dueDate) {
            formatter.dateFormat = "'Tomorrow at' h:mm a"
        } else {
            formatter.dateFormat = "MMM d 'at' h:mm a"
        }
        
        return formatter.string(from: dueDate)
    }
}