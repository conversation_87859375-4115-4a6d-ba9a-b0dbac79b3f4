//
//  TaskExtensions.swift
//  readies
//
//  Created by <PERSON><PERSON> on 2025/6/6.
//

import Foundation
import SwiftData

extension Array where Element == Task {
    // Filter tasks that are due soon or overdue
    var dueSoonOrOverdue: [Task] {
        self.filter { task in
            !task.completed && (task.isOverdue || task.isDueSoon)
        }
    }
    
    // Filter tasks that are completable (not blocked by dependencies)
    func completableTasks() -> [Task] {
        self.filter { task in
            isCompletable(task, allTasks: self)
        }
    }
    
    // Sort tasks by priority (overdue first, then due soon, then by creation date)
    func sortedByPriority() -> [Task] {
        self.sorted { a, b in
            // Completed tasks go to the bottom
            if !a.completed && b.completed { return true }
            if a.completed && !b.completed { return false }
            
            // Among incomplete tasks, prioritize by due date
            if !a.completed && !b.completed {
                // Overdue tasks first
                if a.isOverdue && !b.isOverdue { return true }
                if !a.isOverdue && b.isOverdue { return false }
                
                // Due soon tasks next
                if a.isDueSoon && !b.isDueSoon { return true }
                if !a.isDueSoon && b.isDueSoon { return false }
                
                // Tasks with due dates before tasks without
                if a.dueDate != nil && b.dueDate == nil { return true }
                if a.dueDate == nil && b.dueDate != nil { return false }
                
                // Sort by due date if both have one
                if let aDate = a.dueDate, let bDate = b.dueDate {
                    return aDate < bDate
                }
            }
            
            // Default to creation date
            return a.createdAt > b.createdAt
        }
    }
}

// Helper function to check if a task is completable
func isCompletable(_ task: Task, allTasks: [Task]) -> Bool {
    if task.completed { return false }
    guard let dependencies = task.dependencies else { return true }
    
    return dependencies.allSatisfy { depID in
        allTasks.first { $0.id == depID.value }?.completed ?? false
    }
}