//
//  NotificationManager.swift
//  readies
//
//  Created by <PERSON><PERSON> on 2025/6/6.
//

import Foundation
import UserNotifications
import SwiftUI
import SwiftData

class NotificationManager: NSObject, ObservableObject {
    static let shared = NotificationManager()
    
    private override init() {
        super.init()
        UNUserNotificationCenter.current().delegate = self
    }
    
    func requestAuthorization() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            if granted {
                print("Notification permission granted")
            } else if let error = error {
                print("Notification permission error: \(error.localizedDescription)")
            }
        }
    }
    
    func scheduleNotification(for task: readies.Task) {
        guard let dueDate = task.dueDate,
              !task.completed,
              !task.hasNotified else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Task Due Soon"
        content.body = task.text
        content.sound = .default
        content.categoryIdentifier = "TASK_DUE"
        content.userInfo = ["taskId": task.id.uuidString]
        
        // Schedule notification 1 hour before due date
        let triggerDate = dueDate.addingTimeInterval(-3600)
        
        // Only schedule if the trigger date is in the future
        guard triggerDate > Date() else { return }
        
        let trigger = UNCalendarNotificationTrigger(
            dateMatching: Calendar.current.dateComponents([.year, .month, .day, .hour, .minute], from: triggerDate),
            repeats: false
        )
        
        let request = UNNotificationRequest(
            identifier: task.id.uuidString,
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Error scheduling notification: \(error.localizedDescription)")
            } else {
                print("Notification scheduled for task: \(task.text)")
            }
        }
    }
    
    func cancelNotification(for taskId: UUID) {
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: [taskId.uuidString])
    }
    
    func updateNotification(for task: readies.Task) {
        cancelNotification(for: task.id)
        scheduleNotification(for: task)
    }
    
    func checkAndScheduleAllNotifications(tasks: [readies.Task]) {
        // Cancel all existing notifications first
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
        
        // Schedule notifications for all tasks with due dates
        for task in tasks {
            scheduleNotification(for: task)
        }
    }
}

// MARK: - UNUserNotificationCenterDelegate
extension NotificationManager: UNUserNotificationCenterDelegate {
    func userNotificationCenter(_ center: UNUserNotificationCenter, 
                              willPresent notification: UNNotification, 
                              withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        // Show notification even when app is in foreground
        completionHandler([.banner, .sound, .badge])
    }
    
    func userNotificationCenter(_ center: UNUserNotificationCenter, 
                              didReceive response: UNNotificationResponse, 
                              withCompletionHandler completionHandler: @escaping () -> Void) {
        // Handle notification tap
        if let taskIdString = response.notification.request.content.userInfo["taskId"] as? String,
           let taskId = UUID(uuidString: taskIdString) {
            // Post notification to open task detail
            NotificationCenter.default.post(
                name: .openTaskDetail,
                object: nil,
                userInfo: ["taskId": taskId]
            )
        }
        completionHandler()
    }
}

extension Notification.Name {
    static let openTaskDetail = Notification.Name("openTaskDetail")
}