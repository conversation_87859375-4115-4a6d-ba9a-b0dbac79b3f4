// GeminiAPI.swift
// <PERSON><PERSON> calls to Google Gemini LLM for task generation

import Foundation

class GeminiAPI {
    static let shared = GeminiAPI()
    private let apiKeyKey = "AIzaSyABIjCluxQIgeiK3R0b9b7ZlrUA7KblpIk"

    // Store/retrieve API key in UserDefaults
    var apiKey: String? {
        get { UserDefaults.standard.string(forKey: apiKeyKey) }
        set { UserDefaults.standard.setValue(newValue, forKey: apiKeyKey) }
    }

    // Set the API key (call this before making requests)
    func setAPIKey(_ key: String) {
        apiKey = key
    }

    // Main function to generate tasks from a prompt, expecting JSON output
    func generateTasks(prompt: String) async throws -> [GeneratedTask] {
        guard let apiKey = apiKey, !apiKey.isEmpty else {
            throw GeminiAPIError.missingAPIKey
        }
        // Note: Using gemini-1.5-flash as gemini-2.0-flash is not a valid model name as of now.
        let url = URL(string: "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=\(apiKey)")!

        // Updated prompt asking for JSON
        let fullPrompt = """
        Extract actionable todo items from the following input. Return the result as a JSON array where each object has 'title' (string), 'priority' (integer 0-3, 0=None, 1=Low, 2=Medium, 3=High, default 0), and optionally 'dueDate' (string in YYYY-MM-DD format).

        Input: "\(prompt)"

        JSON Output:
        """

        let body: [String: Any] = [
            "contents": [
                [
                    "parts": [
                        ["text": fullPrompt]
                    ]
                ]
            ],
            // Request JSON output using response_schema
            "generationConfig": [
                "response_mime_type": "application/json", // Use snake_case as per API docs
                "response_schema": [
                    "type": "ARRAY",
                    "items": [
                        "type": "OBJECT",
                        "properties": [
                            "title": ["type": "STRING"],
                            "priority": ["type": "INTEGER"], // Specify integer type
                            "dueDate": ["type": "STRING"]   // Specify string type for date
                        ],
                        "required": ["title"] // Only title is strictly required
                    ]
                ]
            ]
        ]

        let data = try JSONSerialization.data(withJSONObject: body)
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.httpBody = data

        let (responseData, response) = try await URLSession.shared.data(for: request)

        // Debugging: Print response status and raw data
        // if let httpResponse = response as? HTTPURLResponse {
        //     print("Gemini Response Status: \(httpResponse.statusCode)")
        // }
        // print("Gemini Raw Response: \(String(data: responseData, encoding: .utf8) ?? "Invalid UTF-8")")


        let decodedResponse = try JSONDecoder().decode(GeminiResponse.self, from: responseData)

        guard let candidate = decodedResponse.candidates.first,
              let part = candidate.content.parts.first,
              let jsonData = part.text.data(using: .utf8) else {
            throw GeminiAPIError.invalidResponseFormat
        }

        // Decode the JSON text into [GeneratedTask]
        do {
            let tasks = try JSONDecoder().decode([GeneratedTask].self, from: jsonData)
            return tasks
        } catch {
            print("JSON Decoding Error: \(error)")
            // Fallback: Try parsing as simple list if JSON fails
            let fallbackTasks = GeminiAPI.parseTasksFromPlainText(from: part.text)
            return fallbackTasks.map { GeneratedTask(title: $0) }
        }
    }

    // Fallback plain text parsing (renamed from parseTasks)
    static func parseTasksFromPlainText(from text: String) -> [String] {
        let lines = text
            .components(separatedBy: .newlines)
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        // Remove leading bullets/numbers
        return lines.map { line in
            if let range = line.range(of: #"^[-*\d\.\)]*\s*"#, options: .regularExpression) {
                return String(line[range.upperBound...]).trimmingCharacters(in: .whitespaces)
            }
            return line
        }
    }

    enum GeminiAPIError: Error, LocalizedError {
        case missingAPIKey
        case invalidResponseFormat
        var errorDescription: String? {
            switch self {
            case .missingAPIKey: return "Missing Gemini API key."
            case .invalidResponseFormat: return "Invalid response format from LLM."
            }
        }
    }
}

// MARK: - Gemini API Response Models

struct GeminiResponse: Codable {
    let candidates: [GeminiCandidate]
}

struct GeminiCandidate: Codable {
    let content: GeminiContent
}

struct GeminiContent: Codable {
    let parts: [GeminiPart]
}

struct GeminiPart: Codable {
    let text: String
}

// MARK: - Generated Task Structure for JSON

struct GeneratedTask: Codable {
    var title: String
    var priority: Int? // Optional priority
    var dueDate: String? // Optional due date string (YYYY-MM-DD)

    // Helper to convert dueDate string to Date object
    var date: Date? {
        guard let dueDateString = dueDate else { return nil }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = TimeZone.current // Use local time zone
        return formatter.date(from: dueDateString)
    }
}
