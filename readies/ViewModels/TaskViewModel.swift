//
//  TaskViewModel.swift
//  readies
//
//  Created by <PERSON><PERSON> on 2025/6/6.
//

import Foundation
import SwiftData
import SwiftUI

@MainActor
class TaskViewModel: ObservableObject {
    private let modelContext: ModelContext
    private let notificationManager = NotificationManager.shared
    
    init(modelContext: ModelContext) {
        self.modelContext = modelContext
    }
    
    // MARK: - Task Operations
    
    func createTask(text: String, tags: [String], dependencies: [UUID], dueDate: Date?) {
        let tagItems = tags.map { TagItem(name: $0) }
        let dependencyItems = dependencies.isEmpty ? nil : dependencies.map { DependencyID(value: $0) }
        
        let newTask = Task(
            text: text,
            tags: tagItems,
            dependencies: dependencyItems,
            dueDate: dueDate
        )
        
        modelContext.insert(newTask)
        saveContext()
        
        // Schedule notification if due date is set
        if dueDate != nil {
            notificationManager.scheduleNotification(for: newTask)
        }
    }
    
    func updateTask(_ task: Task, text: String? = nil, dueDate: Date? = nil, removeDueDate: Bool = false) {
        if let text = text {
            task.text = text
        }
        
        if removeDueDate {
            task.dueDate = nil
            notificationManager.cancelNotification(for: task.id)
        } else if let dueDate = dueDate {
            task.dueDate = dueDate
            notificationManager.updateNotification(for: task)
        }
        
        saveContext()
    }
    
    func toggleTaskCompletion(_ task: Task) {
        task.completed.toggle()
        
        if task.completed {
            task.completedAt = Date()
            notificationManager.cancelNotification(for: task.id)
        } else {
            task.completedAt = nil
            if task.dueDate != nil {
                notificationManager.scheduleNotification(for: task)
            }
        }
        
        saveContext()
    }
    
    func deleteTask(_ task: Task) {
        notificationManager.cancelNotification(for: task.id)
        modelContext.delete(task)
        saveContext()
    }
    
    func addTag(to task: Task, tagName: String) {
        let trimmedName = tagName.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedName.isEmpty,
              !task.tags.contains(where: { $0.name == trimmedName }) else { return }
        
        task.tags.append(TagItem(name: trimmedName))
        saveContext()
    }
    
    func removeTag(from task: Task, at index: Int) {
        guard task.tags.indices.contains(index) else { return }
        task.tags.remove(at: index)
        saveContext()
    }
    
    func updateDependencies(for task: Task, dependencies: Set<UUID>) {
        task.dependencies = dependencies.isEmpty ? nil : dependencies.map { DependencyID(value: $0) }
        saveContext()
    }
    
    // MARK: - Private Methods
    
    private func saveContext() {
        do {
            try modelContext.save()
        } catch {
            print("Failed to save context: \(error)")
        }
    }
}