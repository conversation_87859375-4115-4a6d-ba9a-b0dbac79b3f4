# Readies - Task Management App

A SwiftUI task management application with dependency tracking, tags, and due date notifications.

## Features

### Core Features

- **Task Management**: Create, edit, and delete tasks
- **Dependencies**: Set task dependencies to track which tasks need to be completed first
- **Tags**: Organize tasks with custom tags
- **Task States**: Visual indicators for task states (Ready, Blocked, Completed)
- **Filtering**: Filter tasks by tags and completion status

### New Features (v2.0)

#### 1. Due Date Management

- Set optional due dates for tasks with date and time
- Visual indicators for overdue and approaching tasks
- Automatic sorting prioritizing urgent tasks

#### 2. Due Soon Section

- Dedicated section at the top showing tasks that are:
  - Overdue (past due date)
  - Due Soon (within 24 hours)
- Horizontal scrolling cards with clear visual indicators
- Automatically hidden when no urgent tasks exist

#### 3. Local Notifications

- Automatic notifications 1 hour before task due time
- Notifications are scheduled when:
  - Creating a new task with a due date
  - Editing a task to add/change due date
- Notifications are cancelled when:
  - Task is completed
  - Task is deleted
  - Due date is removed

## Project Structure

The codebase has been restructured for better organization:

```
readies/
├── Models/
│   ├── Task.swift          # Main task model with due date properties
│   ├── TagItem.swift       # Tag model
│   ├── DependencyID.swift  # Dependency model
│   └── Models.swift        # Legacy compatibility file
├── Views/
│   ├── ContentView.swift   # Main view with due soon section
│   ├── TaskComponents/
│   │   ├── TaskCardView.swift      # Task card with due date display
│   │   └── DueSoonSection.swift    # New due soon section component
│   └── SheetViews/
│       ├── AddTaskView.swift       # Updated with due date picker
│       ├── TaskEditView.swift      # Updated with due date editing
│       └── ...
├── Services/
│   └── NotificationManager.swift   # Handles local notifications
├── ViewModels/
│   └── TaskViewModel.swift         # Business logic for task operations
├── Extensions/
│   └── TaskExtensions.swift        # Helper extensions for task arrays
└── Utils/
    └── Theme.swift                 # Updated with warning colors

```

## Technical Improvements

### 1. Better Code Organization

- Separated models into individual files
- Created dedicated folders for different components
- Added ViewModels for business logic separation
- Created Extensions for reusable functionality

### 2. Enhanced Task Model

- Added `dueDate: Date?` property
- Added `hasNotified: Bool` to track notification state
- Added computed properties:
  - `isOverdue`: Check if task is past due
  - `isDueSoon`: Check if due within 24 hours
  - `formattedDueDate`: Human-readable date format

### 3. Notification System

- Singleton `NotificationManager` for centralized notification handling
- Automatic permission requests on app launch
- Smart notification scheduling and cancellation
- Integration with task lifecycle events

### 4. UI Enhancements

- New warning color in theme for urgent tasks
- Due date indicators in task cards
- Dedicated due soon section with gradient background
- Improved visual hierarchy for urgent tasks

## Usage

### Setting Due Dates

1. When creating a new task, toggle "Set Due Date"
2. Select the desired date and time
3. The task will appear in the "Due Soon" section when appropriate

### Managing Notifications

- Notifications are automatically scheduled 1 hour before due time
- Accept notification permissions when prompted
- Tap on a notification to open the specific task

### Due Soon Section

- Automatically appears when tasks are overdue or due within 24 hours
- Shows tasks in order of urgency (overdue first, then by due date)
- Horizontally scrollable for easy browsing

## Requirements

- iOS 17.0+
- SwiftUI
- SwiftData
- UserNotifications framework

## Future Enhancements

- Recurring tasks
- Custom notification timing
- Task priorities
- Calendar integration
- Widget support
